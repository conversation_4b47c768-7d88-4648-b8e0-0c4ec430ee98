import { prisma } from "@/lib/database";

export interface UserLearningProfile {
  id: string;
  userId: string;
  learningStyle: "visual" | "auditory" | "kinesthetic" | "mixed";
  preferredPace: "slow" | "medium" | "fast";
  difficultyLevel: "beginner" | "intermediate" | "advanced";
  attentionSpan: number;
  preferredHints: boolean;
  skipOptionalSteps: boolean;
  voiceEnabled: boolean;
  adaptiveEnabled: boolean;
  averageCompletionTime: number;
  averageErrorRate: number;
  averageRetryRate: number;
  preferredCategories: string[];
  strongTopics: string[];
  weakTopics: string[];
  peakLearningHours: string[];
  sessionDuration: number;
  breakFrequency: number;
  adaptationHistory: any[];
}

export interface LearningRecommendation {
  id: string;
  userId: string;
  tutorialId?: string;
  type: "tutorial_suggestion" | "difficulty_adjustment" | "pace_change" | "hint_frequency";
  title: string;
  description: string;
  confidence: number;
  reasoning: string;
  metadata: any;
}

export interface AdaptationResult {
  adaptedTutorial: any;
  adaptations: Adaptation[];
  confidence: number;
  reasoning: string[];
}

export interface Adaptation {
  type: "difficulty" | "pace" | "hints" | "content" | "steps";
  original: any;
  adapted: any;
  reason: string;
}

export class AdaptiveLearningService {
  /**
   * Get or create user learning profile
   */
  static async getUserLearningProfile(userId: string): Promise<UserLearningProfile | null> {
    let profile = await prisma.userLearningProfile.findUnique({
      where: { userId },
    });

    if (!profile) {
      // Create default profile based on user's tutorial history
      const userHistory = await this.analyzeUserHistory(userId);
      
      profile = await prisma.userLearningProfile.create({
        data: {
          userId,
          learningStyle: userHistory.inferredLearningStyle || "visual",
          preferredPace: userHistory.inferredPace || "medium",
          difficultyLevel: userHistory.inferredDifficulty || "beginner",
          attentionSpan: userHistory.averageSessionTime || 300,
          preferredCategories: userHistory.preferredCategories || [],
          strongTopics: userHistory.strongTopics || [],
          weakTopics: userHistory.weakTopics || [],
          averageCompletionTime: userHistory.averageCompletionTime || 0,
          averageErrorRate: userHistory.averageErrorRate || 0.0,
          averageRetryRate: userHistory.averageRetryRate || 0.0,
        },
      });
    }

    return profile as UserLearningProfile;
  }

  /**
   * Update user learning profile based on session data
   */
  static async updateLearningProfile(
    userId: string,
    sessionData: {
      tutorialId: string;
      completionTime: number;
      errorRate: number;
      retryRate: number;
      hintsUsed: number;
      stepsSkipped: number;
      satisfactionRating?: number;
    }
  ): Promise<void> {
    const profile = await this.getUserLearningProfile(userId);
    if (!profile) return;

    // Calculate new averages using exponential moving average
    const alpha = 0.3; // Learning rate
    
    const newAverageCompletionTime = profile.averageCompletionTime === 0
      ? sessionData.completionTime
      : Math.round(alpha * sessionData.completionTime + (1 - alpha) * profile.averageCompletionTime);

    const newAverageErrorRate = profile.averageErrorRate === 0
      ? sessionData.errorRate
      : alpha * sessionData.errorRate + (1 - alpha) * profile.averageErrorRate;

    const newAverageRetryRate = profile.averageRetryRate === 0
      ? sessionData.retryRate
      : alpha * sessionData.retryRate + (1 - alpha) * profile.averageRetryRate;

    // Infer learning preferences from behavior
    const adaptations: any[] = [...profile.adaptationHistory];
    
    // Adjust difficulty based on performance
    let newDifficultyLevel = profile.difficultyLevel;
    if (sessionData.errorRate < 0.1 && sessionData.retryRate < 0.05) {
      // User is performing well, might be ready for higher difficulty
      if (profile.difficultyLevel === "beginner") newDifficultyLevel = "intermediate";
      else if (profile.difficultyLevel === "intermediate") newDifficultyLevel = "advanced";
    } else if (sessionData.errorRate > 0.3 || sessionData.retryRate > 0.2) {
      // User is struggling, might need lower difficulty
      if (profile.difficultyLevel === "advanced") newDifficultyLevel = "intermediate";
      else if (profile.difficultyLevel === "intermediate") newDifficultyLevel = "beginner";
    }

    // Adjust pace based on completion time
    let newPreferredPace = profile.preferredPace;
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: sessionData.tutorialId },
      select: { metadata: true },
    });
    
    if (tutorial) {
      const expectedTime = (tutorial.metadata as any)?.estimatedTime * 60 || 300;
      const timeRatio = sessionData.completionTime / expectedTime;
      
      if (timeRatio < 0.7) {
        newPreferredPace = "fast";
      } else if (timeRatio > 1.5) {
        newPreferredPace = "slow";
      } else {
        newPreferredPace = "medium";
      }
    }

    // Adjust hint preferences
    const newPreferredHints = sessionData.hintsUsed > 0;

    // Record adaptation
    adaptations.push({
      timestamp: new Date().toISOString(),
      sessionId: `session-${Date.now()}`,
      changes: {
        difficultyLevel: { from: profile.difficultyLevel, to: newDifficultyLevel },
        preferredPace: { from: profile.preferredPace, to: newPreferredPace },
        preferredHints: { from: profile.preferredHints, to: newPreferredHints },
      },
      performance: {
        completionTime: sessionData.completionTime,
        errorRate: sessionData.errorRate,
        retryRate: sessionData.retryRate,
        satisfactionRating: sessionData.satisfactionRating,
      },
    });

    // Update profile
    await prisma.userLearningProfile.update({
      where: { userId },
      data: {
        difficultyLevel: newDifficultyLevel,
        preferredPace: newPreferredPace,
        preferredHints: newPreferredHints,
        averageCompletionTime: newAverageCompletionTime,
        averageErrorRate: newAverageErrorRate,
        averageRetryRate: newAverageRetryRate,
        adaptationHistory: adaptations,
      },
    });
  }

  /**
   * Adapt tutorial content based on user profile
   */
  static async adaptTutorial(
    tutorialId: string,
    userId: string
  ): Promise<AdaptationResult> {
    const [tutorial, profile] = await Promise.all([
      prisma.tutorial.findUnique({
        where: { id: tutorialId },
        include: { steps: true },
      }),
      this.getUserLearningProfile(userId),
    ]);

    if (!tutorial || !profile) {
      throw new Error("Tutorial or user profile not found");
    }

    const adaptations: Adaptation[] = [];
    const reasoning: string[] = [];
    let adaptedTutorial = { ...tutorial };

    // Adapt difficulty
    if (profile.difficultyLevel !== (tutorial.metadata as any)?.difficulty) {
      const difficultyAdaptation = this.adaptDifficulty(
        tutorial,
        profile.difficultyLevel
      );
      adaptedTutorial = difficultyAdaptation.tutorial;
      adaptations.push(...difficultyAdaptation.adaptations);
      reasoning.push(...difficultyAdaptation.reasoning);
    }

    // Adapt pace
    if (profile.preferredPace !== "medium") {
      const paceAdaptation = this.adaptPace(adaptedTutorial, profile.preferredPace);
      adaptedTutorial = paceAdaptation.tutorial;
      adaptations.push(...paceAdaptation.adaptations);
      reasoning.push(...paceAdaptation.reasoning);
    }

    // Adapt content based on learning style
    const styleAdaptation = this.adaptForLearningStyle(
      adaptedTutorial,
      profile.learningStyle
    );
    adaptedTutorial = styleAdaptation.tutorial;
    adaptations.push(...styleAdaptation.adaptations);
    reasoning.push(...styleAdaptation.reasoning);

    // Adapt hints and help
    if (profile.preferredHints) {
      const hintAdaptation = this.enhanceHints(adaptedTutorial);
      adaptedTutorial = hintAdaptation.tutorial;
      adaptations.push(...hintAdaptation.adaptations);
      reasoning.push(...hintAdaptation.reasoning);
    }

    // Skip optional steps if user prefers
    if (profile.skipOptionalSteps) {
      const stepAdaptation = this.filterOptionalSteps(adaptedTutorial);
      adaptedTutorial = stepAdaptation.tutorial;
      adaptations.push(...stepAdaptation.adaptations);
      reasoning.push(...stepAdaptation.reasoning);
    }

    // Calculate confidence based on profile completeness and adaptation count
    const confidence = Math.min(
      0.5 + (adaptations.length * 0.1) + (profile.adaptationHistory.length * 0.05),
      1.0
    );

    return {
      adaptedTutorial,
      adaptations,
      confidence,
      reasoning,
    };
  }

  /**
   * Generate learning recommendations for user
   */
  static async generateRecommendations(userId: string): Promise<LearningRecommendation[]> {
    const profile = await this.getUserLearningProfile(userId);
    if (!profile) return [];

    const recommendations: LearningRecommendation[] = [];

    // Recommend tutorials based on weak topics
    if (profile.weakTopics.length > 0) {
      const weakTopicTutorials = await prisma.tutorial.findMany({
        where: {
          isActive: true,
          metadata: {
            path: ["category"],
            in: profile.weakTopics,
          },
        },
        take: 3,
      });

      for (const tutorial of weakTopicTutorials) {
        recommendations.push({
          id: `rec-${Date.now()}-${Math.random()}`,
          userId,
          tutorialId: tutorial.id,
          type: "tutorial_suggestion",
          title: `Improve your ${(tutorial.metadata as any)?.category} skills`,
          description: `Based on your learning history, we recommend this tutorial to strengthen your understanding of ${(tutorial.metadata as any)?.category}.`,
          confidence: 0.8,
          reasoning: `User has shown difficulty with ${(tutorial.metadata as any)?.category} topics`,
          metadata: {
            category: (tutorial.metadata as any)?.category,
            difficulty: (tutorial.metadata as any)?.difficulty,
          },
        });
      }
    }

    // Recommend difficulty adjustment
    if (profile.averageErrorRate > 0.25) {
      recommendations.push({
        id: `rec-${Date.now()}-${Math.random()}`,
        userId,
        type: "difficulty_adjustment",
        title: "Consider easier tutorials",
        description: "Your recent performance suggests you might benefit from starting with easier tutorials to build confidence.",
        confidence: 0.7,
        reasoning: `High error rate (${(profile.averageErrorRate * 100).toFixed(1)}%) indicates current difficulty may be too high`,
        metadata: {
          currentDifficulty: profile.difficultyLevel,
          suggestedDifficulty: profile.difficultyLevel === "advanced" ? "intermediate" : "beginner",
        },
      });
    }

    // Recommend pace adjustment
    if (profile.averageCompletionTime > profile.sessionDuration * 1.5) {
      recommendations.push({
        id: `rec-${Date.now()}-${Math.random()}`,
        userId,
        type: "pace_change",
        title: "Adjust learning pace",
        description: "You might benefit from a slower pace to better absorb the material.",
        confidence: 0.6,
        reasoning: "Average completion time exceeds preferred session duration",
        metadata: {
          currentPace: profile.preferredPace,
          suggestedPace: "slow",
        },
      });
    }

    return recommendations;
  }

  /**
   * Analyze user's historical performance
   */
  private static async analyzeUserHistory(userId: string) {
    const [completedTutorials, analytics] = await Promise.all([
      prisma.tutorialProgress.findMany({
        where: { userId, completed: true },
        include: { tutorial: true },
      }),
      prisma.analytics.findMany({
        where: { userId },
        orderBy: { timestamp: "desc" },
        take: 100,
      }),
    ]);

    // Analyze completion patterns
    const categories = completedTutorials.map(t => (t.tutorial.metadata as any)?.category).filter(Boolean);
    const difficulties = completedTutorials.map(t => (t.tutorial.metadata as any)?.difficulty).filter(Boolean);
    const completionTimes = completedTutorials.map(t => t.timeSpent).filter(t => t > 0);

    // Calculate averages and preferences
    const averageCompletionTime = completionTimes.length > 0 
      ? Math.round(completionTimes.reduce((a, b) => a + b, 0) / completionTimes.length)
      : 0;

    const preferredCategories = this.getMostFrequent(categories);
    const inferredDifficulty = this.getMostFrequent(difficulties)[0] || "beginner";

    // Analyze error patterns from analytics
    const errorEvents = analytics.filter(a => a.action.includes("error"));
    const totalEvents = analytics.length;
    const averageErrorRate = totalEvents > 0 ? errorEvents.length / totalEvents : 0;

    return {
      averageCompletionTime,
      preferredCategories,
      inferredDifficulty,
      inferredLearningStyle: "visual", // Default, could be inferred from interaction patterns
      inferredPace: averageCompletionTime > 600 ? "slow" : averageCompletionTime < 300 ? "fast" : "medium",
      averageErrorRate,
      averageRetryRate: 0, // Would need retry tracking
      strongTopics: preferredCategories.slice(0, 3),
      weakTopics: [], // Would need failure analysis
      averageSessionTime: averageCompletionTime,
    };
  }

  private static getMostFrequent(arr: string[]): string[] {
    const frequency: { [key: string]: number } = {};
    arr.forEach(item => {
      frequency[item] = (frequency[item] || 0) + 1;
    });
    
    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .map(([item]) => item);
  }

  private static adaptDifficulty(tutorial: any, targetDifficulty: string) {
    // Implementation for difficulty adaptation
    return {
      tutorial,
      adaptations: [],
      reasoning: [`Adapted difficulty to ${targetDifficulty}`],
    };
  }

  private static adaptPace(tutorial: any, pace: string) {
    // Implementation for pace adaptation
    return {
      tutorial,
      adaptations: [],
      reasoning: [`Adapted pace to ${pace}`],
    };
  }

  private static adaptForLearningStyle(tutorial: any, style: string) {
    // Implementation for learning style adaptation
    return {
      tutorial,
      adaptations: [],
      reasoning: [`Adapted content for ${style} learning style`],
    };
  }

  private static enhanceHints(tutorial: any) {
    // Implementation for hint enhancement
    return {
      tutorial,
      adaptations: [],
      reasoning: ["Enhanced hints and help content"],
    };
  }

  private static filterOptionalSteps(tutorial: any) {
    // Implementation for optional step filtering
    return {
      tutorial,
      adaptations: [],
      reasoning: ["Filtered out optional steps"],
    };
  }
}
